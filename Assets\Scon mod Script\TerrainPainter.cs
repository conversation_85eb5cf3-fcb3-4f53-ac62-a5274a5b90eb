using UnityEngine;
using UnityEngine.UI;

public enum AreaSize
{
    Area_20x20,
    Area_50x50,
    Area_100x100,
    Custom
}

public class TerrainPainter : MonoBehaviour
{
    public ParticleSystem MudParticles;
    public float paintRadius = 2f;
    public int targetLayerIndex = 0;     // Texture layer to check before painting (e.g. grass)
    public int applyLayerIndex = 1;      // Texture layer to apply (e.g. soil)
    public float minSpeed = 0.1f;        // Minimum speed to start painting
    public float maxSpeed = 10f;         // Maximum speed for painting calculations
    public float paintIntensity = 1f;    // How strong the painting effect is
    public bool useSquareBrush = true;   // Use square brush instead of circular
    public Vector3 paintOffset = Vector3.zero; // Manual offset for paint position
    public bool showDebugGizmos = false; // Show debug gizmos for paint position
    public float paintFrequency = 0.1f;  // How often to paint (lower = more frequent)

    [Header("Area Completion Settings")]
    public AreaSize areaSize = AreaSize.Area_50x50;
    public Vector2 customAreaSize = new Vector2(50, 50);
    public GameObject completionPanel; // Panel to show when area is complete
    public Text percentageText; // UI Text to show current percentage
    public float targetCompletionPercentage = 1000f; // Target percentage for completion

    [Header("Area Progress")]
    public float currentCompletionPercentage = 0f;
    public bool areaCompleted = false;

    private Terrain terrain;
    private TerrainData terrainData;
    private float[,,] originalAlphamaps;
    private Vector3 lastPosition;
    private float currentSpeed;
    private Vector3 velocity;
    private Renderer objectRenderer;
    private float lastPaintTime;
    private bool isPainting = false; // Track painting state for particles

    // Area tracking variables
    private Vector2 currentAreaSize;
    private int totalAreaPixels;
    private int paintedPixels;
    

    void Start()
    {
        terrain = Terrain.activeTerrain;
        objectRenderer = GetComponent<Renderer>();

        if (terrain != null)
        {
            terrainData = terrain.terrainData;
            originalAlphamaps = terrainData.GetAlphamaps(0, 0,
                terrainData.alphamapWidth, terrainData.alphamapHeight);
        }

        lastPosition = GetObjectCenter();

        // Initialize area tracking
        InitializeAreaTracking();
    }

    Vector3 GetObjectCenter()
    {
        // Always use the same method to get object center for consistency
        if (objectRenderer != null)
        {
            return objectRenderer.bounds.center;
        }
        return transform.position;
    }

    void Update()
    {
        // Calculate current speed and velocity for better curve detection using consistent center
        Vector3 currentCenter = GetObjectCenter();
        velocity = (currentCenter - lastPosition) / Time.deltaTime;
        currentSpeed = velocity.magnitude;
        lastPosition = currentCenter;

        bool shouldPaint = false;

        // Check for terrain contact and paint directly at current position
        if (currentSpeed >= minSpeed)
        {
            shouldPaint = CheckTerrainContactSmooth();
        }

        // Control mud particles based on painting state
        ControlMudParticles(shouldPaint);

        // Update UI text every frame for real-time display
        UpdateUIText();

        // Reset terrain with R key
        if (Input.GetKeyDown(KeyCode.R))
        {
            terrainData.SetAlphamaps(0, 0, originalAlphamaps);
        }
    }

    bool CheckTerrainContactSmooth()
    {
        if (terrain == null) return false;

        // Get consistent object center position
        Vector3 objectCenter = GetObjectCenter();

        // Raycast downward from object center to check if we're on terrain
        Vector3 rayStart = objectCenter + Vector3.up * 0.5f;

        if (Physics.Raycast(rayStart, Vector3.down, out RaycastHit hit, 2f))
        {
            // Check if we hit the terrain
            if (hit.collider.GetComponent<TerrainCollider>())
            {

                // Use object's center X,Z position but terrain's Y position for accurate painting
                Vector3 exactPaintPos = new Vector3(objectCenter.x + paintOffset.x, hit.point.y, objectCenter.z + paintOffset.z);

                // Check if enough time has passed since last paint to avoid dotted pattern
                if (Time.time - lastPaintTime >= paintFrequency)
                {
                    // Paint directly at current position - NO TRAIL
                    if (IsTargetLayer(exactPaintPos))
                    {
                        PaintTextureAtWithIntensity(exactPaintPos, paintIntensity);
                        lastPaintTime = Time.time;
                        return true; // Painting is happening
                    }
                    lastPaintTime = Time.time;
                }
            }
        }
        return false; // No painting happening
    }



    // Alternative collision method (keep both for better compatibility)
    void OnCollisionStay(Collision collision)
    {
        if (terrain == null) return;

        // Check if colliding with Terrain
        if (collision.collider.GetComponent<TerrainCollider>())
        {
            // Loop through all contact points with terrain
            foreach (ContactPoint contact in collision.contacts)
            {
                if (IsTargetLayer(contact.point))
                {
                    PaintTextureAt(contact.point);
                }
            }
        }
    }

    bool IsTargetLayer(Vector3 worldPos)
    {
        Vector3 terrainPos = worldPos - terrain.transform.position;

        int mapX = Mathf.RoundToInt((terrainPos.x / terrainData.size.x) * terrainData.alphamapWidth);
        int mapZ = Mathf.RoundToInt((terrainPos.z / terrainData.size.z) * terrainData.alphamapHeight);

        float[,,] alphamaps = terrainData.GetAlphamaps(mapX, mapZ, 1, 1);
        float targetWeight = alphamaps[0, 0, targetLayerIndex];

        return targetWeight >= 0.5f;
    }

    void PaintTextureAt(Vector3 worldPos)
    {
        PaintTextureAtWithIntensity(worldPos, 1f);
    }

    void PaintTextureAtWithIntensity(Vector3 worldPos, float intensity)
    {
        Vector3 terrainPos = worldPos - terrain.transform.position;

        // Convert world position to alphamap coordinates
        float mapX = terrainPos.x / terrainData.size.x * terrainData.alphamapWidth;
        float mapZ = terrainPos.z / terrainData.size.z * terrainData.alphamapHeight;

        // Calculate brush size in alphamap units for square pattern
        float brushSizeX = paintRadius / terrainData.size.x * terrainData.alphamapWidth;
        float brushSizeZ = paintRadius / terrainData.size.z * terrainData.alphamapHeight;

        // Use consistent brush size for perfect square
        float brushSize = Mathf.Max(brushSizeX, brushSizeZ);
        int halfBrush = Mathf.RoundToInt(brushSize * 0.5f);

        // Calculate square bounds
        int centerX = Mathf.FloorToInt(mapX);
        int centerZ = Mathf.FloorToInt(mapZ);

        int startX = Mathf.Clamp(centerX - halfBrush, 0, terrainData.alphamapWidth - 1);
        int startZ = Mathf.Clamp(centerZ - halfBrush, 0, terrainData.alphamapHeight - 1);
        int endX = Mathf.Clamp(centerX + halfBrush, 0, terrainData.alphamapWidth - 1);
        int endZ = Mathf.Clamp(centerZ + halfBrush, 0, terrainData.alphamapHeight - 1);

        int actualWidth = endX - startX + 1;
        int actualHeight = endZ - startZ + 1;

        float[,,] alphaMaps = terrainData.GetAlphamaps(startX, startZ, actualWidth, actualHeight);

        // Paint with selected brush pattern
        for (int x = 0; x < actualWidth; x++)
        {
            for (int z = 0; z < actualHeight; z++)
            {
                float currentWeight = alphaMaps[x, z, targetLayerIndex];
                if (currentWeight >= 0.5f)
                {
                    float falloff = 1f;

                    if (useSquareBrush)
                    {
                        // Simple square brush - no rotation
                        falloff = 1f;
                    }
                    else
                    {
                        // Circular brush with distance-based falloff
                        float centerDistX = x - (actualWidth * 0.5f);
                        float centerDistZ = z - (actualHeight * 0.5f);
                        float distance = Mathf.Sqrt(centerDistX * centerDistX + centerDistZ * centerDistZ);
                        float maxRadius = Mathf.Min(actualWidth, actualHeight) * 0.5f;

                        if (distance <= maxRadius)
                        {
                            falloff = 1f - (distance / maxRadius);
                        }
                        else
                        {
                            continue; // Skip pixels outside circle
                        }
                    }

                    falloff = Mathf.Clamp01(falloff * intensity);

                    // Apply texture blending
                    for (int i = 0; i < terrainData.alphamapLayers; i++)
                    {
                        if (i == applyLayerIndex)
                        {
                            alphaMaps[x, z, i] = Mathf.Lerp(alphaMaps[x, z, i], 1f, falloff);
                            // Count painted pixels for area completion
                            if (alphaMaps[x, z, i] > 0.5f)
                            {
                                paintedPixels++;
                            }
                        }
                        else
                        {
                            alphaMaps[x, z, i] = Mathf.Lerp(alphaMaps[x, z, i], 0f, falloff);
                        }
                    }
                }
            }
        }

        terrainData.SetAlphamaps(startX, startZ, alphaMaps);

        // Update area progress
        UpdateAreaProgress();
    }

    void OnDrawGizmos()
    {
        if (showDebugGizmos && Application.isPlaying)
        {
            // Show object center using consistent method
            Gizmos.color = Color.blue;
            Vector3 objectCenter = GetObjectCenter();
            Gizmos.DrawWireSphere(objectCenter, 0.5f);

            // Show paint position with offset
            Vector3 paintPos = objectCenter + paintOffset;

            // Show brush gizmo
            Gizmos.color = Color.red;
            if (useSquareBrush)
            {
                Gizmos.DrawWireCube(paintPos, Vector3.one * paintRadius * 2f);
            }
            else
            {
                Gizmos.DrawWireSphere(paintPos, paintRadius);
            }
        }
    }

    void InitializeAreaTracking()
    {
        // Get area size based on selection
        switch (areaSize)
        {
            case AreaSize.Area_20x20:
                currentAreaSize = new Vector2(20, 20);
                break;
            case AreaSize.Area_50x50:
                currentAreaSize = new Vector2(50, 50);
                break;
            case AreaSize.Area_100x100:
                currentAreaSize = new Vector2(100, 100);
                break;
            case AreaSize.Custom:
                currentAreaSize = customAreaSize;
                break;
        }

        // Calculate total pixels in the area
        float pixelsPerMeterX = terrainData.alphamapWidth / terrainData.size.x;
        float pixelsPerMeterZ = terrainData.alphamapHeight / terrainData.size.z;
        totalAreaPixels = Mathf.RoundToInt(currentAreaSize.x * pixelsPerMeterX * currentAreaSize.y * pixelsPerMeterZ);

        paintedPixels = 0;
        currentCompletionPercentage = 0f;
        areaCompleted = false;

        // Initialize UI Text
        if (percentageText != null)
        {
            percentageText.text = "0.0%";
        }

        if (completionPanel != null)
        {
            completionPanel.SetActive(false);
        }
    }

    void UpdateAreaProgress()
    {
        if (areaCompleted) return;

        // Calculate completion percentage
        if (totalAreaPixels > 0)
        {
            float completionRatio = (float)paintedPixels / totalAreaPixels;
            currentCompletionPercentage = completionRatio * targetCompletionPercentage;

            // Update UI Text
            if (percentageText != null)
            {
                percentageText.text = currentCompletionPercentage.ToString("F1") + "%";
            }

            // Check if area is completed
            if (currentCompletionPercentage >= targetCompletionPercentage)
            {
                areaCompleted = true;
                currentCompletionPercentage = targetCompletionPercentage;

                // Update final percentage text
                if (percentageText != null)
                {
                    percentageText.text = targetCompletionPercentage.ToString("F0") + "%";
                }

                // Show completion panel
                if (completionPanel != null)
                {
                    completionPanel.SetActive(true);
                }

                Debug.Log("Area Completed! Percentage: " + currentCompletionPercentage + "%");
            }
        }
    }

    [ContextMenu("Reset Area Progress")]
    public void ResetAreaProgress()
    {
        paintedPixels = 0;
        currentCompletionPercentage = 0f;
        areaCompleted = false;

        // Reset UI Text
        if (percentageText != null)
        {
            percentageText.text = "0.0%";
        }

        if (completionPanel != null)
        {
            completionPanel.SetActive(false);
        }

        Debug.Log("Area progress reset!");
    }

    void UpdateUIText()
    {
        if (percentageText != null && !areaCompleted)
        {
            // Calculate current percentage for display
            if (totalAreaPixels > 0)
            {
                float completionRatio = (float)paintedPixels / totalAreaPixels;
                float displayPercentage = completionRatio * targetCompletionPercentage;
                percentageText.text = displayPercentage.ToString("F1") + "%";
            }
            else
            {
                percentageText.text = "0.0%";
            }
        }
    }

}
